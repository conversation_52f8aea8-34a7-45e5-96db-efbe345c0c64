"use client";

import React, { useState } from 'react';
import {
  <PERSON><PERSON>,
  <PERSON><PERSON><PERSON>ontent,
  <PERSON><PERSON>Header,
  <PERSON><PERSON>Title,
  DialogFooter,
  DialogDescription,
} from "@/components/ui/dialog";
import { Button } from '@/components/ui/button';
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import { Alert, AlertDescription } from '@/components/ui/alert';
import { Loader2, AlertTriangle, X, CheckCircle } from 'lucide-react';

interface BulkStatusUpdateModalProps {
  entityIds: string[];
  isOpen: boolean;
  onClose: () => void;
  onSuccess: (updatedIds: string[], newStatus: string) => void;
}

const statusOptions = ['ACTIVE', 'PENDING', 'REJECTED', 'INACTIVE', 'ARCHIVED', 'NEEDS_REVISION'];

export const BulkStatusUpdateModal: React.FC<BulkStatusUpdateModalProps> = ({
  entityIds,
  isOpen,
  onClose,
  onSuccess,
}) => {
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [newStatus, setNewStatus] = useState<string>('');

  const handleUpdate = async () => {
    // This is a placeholder for the API call
    console.log(`Bulk updating ${entityIds.length} entities to ${newStatus}`);
    
    // Simulate API call
    setLoading(true);
    setError(null);
    await new Promise(resolve => setTimeout(resolve, 1000));
    setLoading(false);

    onSuccess(entityIds, newStatus);
    onClose();
  };

  const handleClose = () => {
    if (!loading) {
      setNewStatus('');
      setError(null);
      onClose();
    }
  };

  if (!isOpen) return null;

  return (
    <Dialog open={isOpen} onOpenChange={handleClose}>
      <DialogContent className="max-w-md">
        <DialogHeader>
          <DialogTitle className="flex items-center">
            <CheckCircle className="w-5 h-5 mr-2 text-blue-600" />
            Bulk Status Update
          </DialogTitle>
          <DialogDescription>
            Update the status for {entityIds.length} selected entities.
          </DialogDescription>
        </DialogHeader>

        <div className="space-y-4">
          <p>
            You are about to change the status for <strong>{entityIds.length}</strong> entities.
            Please select the new status below.
          </p>

          <Select value={newStatus} onValueChange={setNewStatus}>
            <SelectTrigger>
              <SelectValue placeholder="Select new status" />
            </SelectTrigger>
            <SelectContent>
              {statusOptions.map(status => (
                <SelectItem key={status} value={status}>{status}</SelectItem>
              ))}
            </SelectContent>
          </Select>

          {error && (
            <Alert className="border-red-200 bg-red-50">
              <AlertTriangle className="h-4 w-4 text-red-600" />
              <AlertDescription className="text-red-800">
                {error}
              </AlertDescription>
            </Alert>
          )}
        </div>

        <DialogFooter>
          <Button
            type="button"
            variant="outline"
            onClick={handleClose}
            disabled={loading}
          >
            <X className="w-4 h-4 mr-2" />
            Cancel
          </Button>
          <Button
            type="button"
            onClick={handleUpdate}
            disabled={loading || !newStatus}
            className="bg-blue-600 hover:bg-blue-700"
          >
            {loading ? (
              <Loader2 className="w-4 h-4 mr-2 animate-spin" />
            ) : (
              <CheckCircle className="w-4 h-4 mr-2" />
            )}
            Update Status
          </Button>
        </DialogFooter>
      </DialogContent>
    </Dialog>
  );
};